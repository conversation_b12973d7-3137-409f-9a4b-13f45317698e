package com.noosh.app.mapper.reason;

import com.noosh.app.commons.dto.reason.WorkgroupQuoteReasonDTO;
import com.noosh.app.commons.entity.reason.WorkgroupReason;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = {QuoteReasonMapper.class})
public interface WorkgroupQuoteReasonMapper {

    @Mapping(target = "reason", source = "reason")
    WorkgroupQuoteReasonDTO toDTO(WorkgroupReason workgroupReason);

    List<WorkgroupQuoteReasonDTO> toDTOs(List<WorkgroupReason> workgroupReasons);
}
